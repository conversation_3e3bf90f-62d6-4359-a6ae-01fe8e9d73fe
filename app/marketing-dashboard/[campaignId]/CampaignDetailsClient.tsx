"use client";

import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useEffect, useState } from "react";

import { AppSidebar } from "@/components/app-sidebar";
import { CampaignInfo } from "@/components/campaign-detail/CampaignInfo";
import React from "react";
import { SiteHeader } from "@/components/site-header";
import { Skeleton } from "@/components/ui/skeleton";
import { SpendBreakdown } from "@/components/campaign-detail/SpendBreakdown";
import { SummaryCards } from "@/components/campaign-detail/SummaryCards";

// Define the type for CampaignDetails
export interface CampaignDetails {
  id: string;
  name: string;
  brand_name?: string;
  status?: string;
  budget?: number | null;
  spend: number | null;
  impressions?: number | null;
  clicks?: number | null;
  ctr?: string | null;
  cpc?: string | null;
  conversions?: number | null;
  cpa?: string | null;
  roas?: number | null;
  startDate?: string | null;
  endDate?: string | null;
  targetAudience?: string | null;
  channels?: string[] | null;
  notes?: string | null;
  FacebookSpend?: number | null;
  InstagramSpend?: number | null;
  GoogleSpend?: number | null;
  TikTokSpend?: number | null;
  AmazonSpend?: number | null;
  AwarenessSpend?: number | null;
  ConversionSpend?: number | null;
  RetargetingSpend?: number | null;
  SeasonalSpend?: number | null;
  totalConversionValue?: number | null;
  totalSpend?: number | null;
  dailyData?: Array<{
    date: string;
    impressions?: number;
    clicks?: number;
    spend?: number;
    conversions?: number;
    [key: string]: string | number | undefined;
  }>;
}

interface CampaignDetailsClientProps {
  params: Promise<{
    campaignId: string;
  }>;
}

export default function CampaignDetailsClient({ params }: CampaignDetailsClientProps) {
  // Use React.use to unwrap the params Promise before accessing properties
  const unwrappedParams = React.use(params);
  const campaignId = unwrappedParams.campaignId;
  const [campaignDetails, setCampaignDetails] = useState<CampaignDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [defaultCurrency] = useState<string>("CAD");

  // Define the message to display while loading
  const loadingMessage = "Loading campaign data...";

  useEffect(() => {
    async function loadCampaignDetails() {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch campaign details from the API route
        const response = await fetch(`/api/marketing/campaign-data/${campaignId}`);
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Failed to fetch campaign details: ${response.status}`);
        }
        
        const details = await response.json();
        setCampaignDetails(details);
      } catch (e: unknown) {
        if (e instanceof Error) {
          setError(e.message);
        } else {
          setError("An unknown error occurred");
        }
        console.error("Error fetching campaign details:", e);
      } finally {
        setLoading(false);
      }
    }

    loadCampaignDetails();
  }, [campaignId]);

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader 
          breadcrumbRootName="Marketing Dashboard" 
          breadcrumbRootPath="/marketing-dashboard" 
        />
        <div className="flex flex-1 flex-col">
          <main className="flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
            {/* Header Section */}
            <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
              <div>
                <h1 className="text-2xl font-bold tracking-tight">
                  {loading ? loadingMessage : (campaignDetails?.name || "Campaign Details")}
                </h1>
                {!loading && campaignDetails && (
                  <p className="text-muted-foreground">
                    {campaignDetails.brand_name ? `Brand: ${campaignDetails.brand_name}` : ""}
                    {campaignDetails.startDate && campaignDetails.endDate && (
                      <> • {campaignDetails.startDate} - {campaignDetails.endDate}</>
                    )}
                  </p>
                )}
              </div>
            </div>

            {/* Error State */}
            {error && (
              <div className="p-4 bg-destructive/10 border border-destructive rounded-md">
                <p className="text-destructive">Could not load campaign details: {error}</p>
              </div>
            )}

            {/* Loading State */}
            {loading && (
              <div className="space-y-4">
                <Skeleton className="h-8 w-[250px]" />
                <Skeleton className="h-[100px] w-full" />
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <Skeleton className="h-[180px] rounded-lg" />
                  <Skeleton className="h-[180px] rounded-lg" />
                  <Skeleton className="h-[180px] rounded-lg" />
                  <Skeleton className="h-[180px] rounded-lg" />
                </div>
              </div>
            )}

            {/* Campaign Content - Only shown when not loading and no error */}
            {!loading && !error && campaignDetails && (
              <div className="space-y-6">
                {/* KPI Summary Cards */}
                <SummaryCards campaign={campaignDetails} currency={defaultCurrency} />
                
                {/* Campaign Information */}
                <CampaignInfo campaign={campaignDetails} />
                
                {/* Spend Breakdown */}
                <SpendBreakdown campaign={campaignDetails} currency={defaultCurrency} />
              </div>
            )}
          </main>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}