import CampaignDetailsClient from "./CampaignDetailsClient";
import { authOptions } from "@/lib/auth-options";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

interface CampaignDetailsPageProps {
  params: Promise<{
    campaignId: string;
  }>;
}

export default async function CampaignDetailsPage({ params }: CampaignDetailsPageProps) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    redirect("/auth/signin");
  }

  return <CampaignDetailsClient params={params} />;
}
