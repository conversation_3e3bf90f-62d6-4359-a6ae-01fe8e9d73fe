"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card"
import { ColumnDef, SortingState } from "@tanstack/react-table"
import React, { useEffect, useMemo, useState } from "react"
import { formatCurrency, formatDate, formatNumber, formatPercent } from "@/lib/chart-utils"

import { DataTable } from "./data-table" // Assuming data-table.tsx is in the same directory
import Link from "next/link" // Added for navigation
import { Skeleton } from "./ui/skeleton"
import { useCampaignData } from "@/lib/api/campaign-data-service"
import { useFilters } from "@/lib/contexts/filter-context"

// Using CampaignDataResponse from campaign-data-service.ts instead of defining a separate interface

// Define the structure of a single row of campaign data
// This should align with the data returned by /api/marketing/campaign-data
export interface CampaignDataRow {
  id: string; // Unique identifier for the row, required by DataTable
  date: string;
  campaign_name: string;
  brand_name: string;
  sales_channel_type: string;
  // Platform-specific spend (e.g., FacebookSpend, GoogleSpend) - will be handled if API provides them flatly or dynamically
  totalSpend: number | null; // Changed from total_spend
  // Campaign type-specific spend (e.g., AwarenessSpend, ConversionSpend) - similar to platform spend
  totalImpressions: number | null; // Changed from impressions
  totalClicks: number | null;      // Changed from clicks
  ctr?: number | null; // Calculated: totalClicks / totalImpressions
  totalConversions: number | null; // Changed from conversions
  totalConversionValue: number | null; // Changed from conversion_value
  roas?: number | null; // From API or calculated: totalConversionValue / totalSpend
  cpa?: number | null;  // From API or calculated: totalSpend / totalConversions
  // Other potential fields from API:
  // facebook_spend?: number | null;
  // google_spend?: number | null;
  // awareness_spend?: number | null;
  // conversion_spend?: number | null;
}

export function CampaignDataTable() {
  const { filters, getQueryParams } = useFilters()
  const [data, setData] = useState<CampaignDataRow[]>([])
  const [error, setError] = useState<string | null>(null)
  const [sorting, setSorting] = useState<SortingState>([])

  // Use our campaign data service with base params (without sorting)
  const baseQueryParams = getQueryParams()
  const baseQueryParamsString = new URLSearchParams(baseQueryParams).toString()
  const { data: campaignData, loading, error: apiError } = useCampaignData(baseQueryParamsString)
  
  const currentCurrency = filters.currency

  // Process campaign data when it's loaded
  useEffect(() => {
    if (loading) {
      // Reset data while loading
      setData([])
      setError(null)
      return
    }

    if (apiError) {
      console.error("API Error:", apiError)
      setError(apiError)
      setData([])
      return
    }

    try {
      if (!Array.isArray(campaignData) || campaignData.length === 0) {
        console.warn("No campaign data returned from API or data is not an array.")
        setData([])
        return
      }

      // Transform campaignData to CampaignDataRow[], ensuring 'id' and calculations
      const processedData = campaignData.map((item, index) => {
        // Access API fields using camelCase as specified in the task
        const totalSpend = item.totalSpend ?? 0
        const totalImpressions = item.totalImpressions ?? 0
        const totalClicks = item.totalClicks ?? 0
        const totalConversions = item.totalConversions ?? 0
        const totalConversionValue = item.totalConversionValue ?? 0
        
        // API provides roas and cpa, use those directly. Fallback if not present.
        const apiRoas = item.roas
        const apiCpa = item.cpa

        // Ensure 'id' is unique. If API doesn't provide one, create a composite or use index.
        const id = item.id || `${item.date || ''}-${item.campaign_name || 'N/A'}-${index}`

        return {
          id,
          date: item.date || '',
          campaign_name: item.campaign_name || '',
          brand_name: item.brand_name || '',
          sales_channel_type: item.sales_channel_type || '',
          
          totalSpend,
          totalImpressions,
          totalClicks,
          ctr: totalImpressions > 0 ? totalClicks / totalImpressions : 0,
          totalConversions,
          totalConversionValue,
          roas: apiRoas !== undefined ? apiRoas : (totalSpend > 0 ? totalConversionValue / totalSpend : 0),
          cpa: apiCpa !== undefined ? apiCpa : (totalConversions > 0 ? totalSpend / totalConversions : 0),
        } as CampaignDataRow
      })
      
      setData(processedData)
    } catch (err) {
      console.error("Failed to process campaign data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      setData([])
    }
  }, [campaignData, loading, apiError])

  const columns = useMemo<ColumnDef<CampaignDataRow, unknown>[]>(() => [
    { accessorKey: "date", header: "Date", cell: ({ row }) => formatDate(row.original.date) },
    {
      accessorKey: "campaign_name",
      header: "Campaign Name",
      cell: ({ row }) => {
        const campaign = row.original
        const campaignName = campaign.campaign_name
        
        if (!campaignName) return <span>N/A</span>;
        
        // Create a URL-safe slug from the campaign name
        // This approach is more robust than simple encoding
        const createCampaignSlug = (name: string): string => {
          // First replace problematic characters with dashes
          const slug = name
            .toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove all non-word chars except spaces and dashes
            .replace(/[\s_]+/g, '-')   // Replace spaces and underscores with dashes
            .replace(/--+/g, '-')      // Replace multiple dashes with single dash
            .trim();
          
          // Ensure we have a unique identifier by adding a hash
          // This helps avoid collisions between similarly named campaigns
          const hashCode = (str: string): number => {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
              hash = (hash << 5) - hash + str.charCodeAt(i);
              hash |= 0; // Convert to 32-bit integer
            }
            return Math.abs(hash);
          };
          
          // Add a short hash at the end for uniqueness
          const hash = hashCode(name).toString(36).substring(0, 4);
          return `${slug}-${hash}`;
        };
        
        const campaignSlug = createCampaignSlug(campaignName);
        
        // Truncate campaign name if it's longer than 25 characters
        const maxLength = 25
        const displayName = campaignName.length > maxLength 
          ? `${campaignName.substring(0, maxLength)}...` 
          : campaignName
        
        return (
          <div className="relative group">
            <Link 
              href={`/marketing-dashboard/${campaignSlug}`} 
              className="hover:underline text-primary"
              title={campaignName} // HTML tooltip for accessibility
              prefetch={false} // Disable prefetch to avoid unnecessary API calls
            >
              {displayName}
            </Link>
            {campaignName.length > maxLength && (
              <div className="absolute z-50 hidden group-hover:block bg-black text-white text-sm rounded p-2 -mt-1 min-w-max max-w-xs">
                {campaignName}
              </div>
            )}
          </div>
        )
      }
    },
    { accessorKey: "brand_name", header: "Brand" },
    { accessorKey: "sales_channel_type", header: "Sales Channel" },
    { 
      accessorKey: "totalSpend", 
      header: "Total Spend",
      cell: ({ row }) => formatCurrency(row.original.totalSpend, currentCurrency) 
    },
    { 
      accessorKey: "totalImpressions", 
      header: "Impressions",
      cell: ({ row }) => formatNumber(row.original.totalImpressions)
    },
    { 
      accessorKey: "totalClicks", 
      header: "Clicks",
      cell: ({ row }) => formatNumber(row.original.totalClicks)
    },
    { 
      accessorKey: "ctr", 
      header: "CTR",
      cell: ({ row }) => formatPercent(row.original.ctr ?? null),
      sortingFn: (rowA, rowB) => {
        const valueA = rowA.original.ctr ?? 0
        const valueB = rowB.original.ctr ?? 0
        return valueA > valueB ? 1 : valueA < valueB ? -1 : 0
      }
    },
    {
      accessorKey: "totalConversions", 
      header: "Conversions",
      cell: ({ row }) => formatNumber(row.original.totalConversions ?? null)
    },
    {
      accessorKey: "totalConversionValue", 
      header: "Conversion Value",
      cell: ({ row }) => formatCurrency(row.original.totalConversionValue ?? null, currentCurrency)
    },
    {
      accessorKey: "roas", 
      header: "ROAS",
      cell: ({ row }) => {
        const roas = row.original.roas;
        return typeof roas === 'number' ? `${roas.toFixed(2)}x` : "N/A";
      },
      sortingFn: (rowA, rowB) => {
        const valueA = rowA.original.roas ?? 0
        const valueB = rowB.original.roas ?? 0
        return valueA > valueB ? 1 : valueA < valueB ? -1 : 0
      }
    },
    {
      accessorKey: "cpa",
      header: "CPA",
      cell: ({ row }) => formatCurrency(row.original.cpa ?? null, currentCurrency),
      sortingFn: (rowA, rowB) => {
        const valueA = rowA.original.cpa ?? 0
        const valueB = rowB.original.cpa ?? 0
        return valueA > valueB ? 1 : valueA < valueB ? -1 : 0
      }
    },
  ], [currentCurrency])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Campaign Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Campaign Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">Error loading campaign data: {error}</p>
        </CardContent>
      </Card>
    )
  }
  
  if (!data.length && !loading) {
     return (
      <Card>
        <CardHeader>
          <CardTitle>Campaign Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <p>No campaign data available for the selected filters.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Campaign Performance Details</CardTitle>
      </CardHeader>
      <CardContent>
        <DataTable 
          columns={columns} 
          data={data} 
          enableRowSelection={false} 
          state={{ sorting }}
          onSortingChange={setSorting}
          manualSorting={false} // Changed to false to use client-side sorting
          enableMultiSort={false} // Ensure only one column can be sorted at a time
        />
      </CardContent>
    </Card>
  )
}
