"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

import { CampaignDetails } from "@/lib/api/campaign-detail-service";
import { formatDate } from "@/lib/chart-utils";

interface CampaignInfoProps {
  campaign: CampaignDetails;
}

export function CampaignInfo({ campaign }: CampaignInfoProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Campaign Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Campaign Name</h3>
            <p className="mt-1">{campaign.name}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Brand</h3>
            <p className="mt-1">{campaign.brand_name || "N/A"}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
            <p className="mt-1">{campaign.status || "N/A"}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Start Date</h3>
            <p className="mt-1">{campaign.startDate ? formatDate(campaign.startDate) : "N/A"}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">End Date</h3>
            <p className="mt-1">{campaign.endDate ? formatDate(campaign.endDate) : "N/A"}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Target Audience</h3>
            <p className="mt-1">{campaign.targetAudience || "N/A"}</p>
          </div>
          <div className="md:col-span-2 lg:col-span-3">
            <h3 className="text-sm font-medium text-muted-foreground">Channels</h3>
            <p className="mt-1">{(campaign.channels && campaign.channels.length > 0) ? campaign.channels.join(", ") : "N/A"}</p>
          </div>
          <div className="md:col-span-2 lg:col-span-3">
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p className="mt-1">{campaign.notes || "No notes available"}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
