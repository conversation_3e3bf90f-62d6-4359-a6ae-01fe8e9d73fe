"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency, formatDate, formatNumber } from "@/lib/chart-utils";

import { CampaignDetails } from "@/lib/api/campaign-detail-service";

interface SummaryCardsProps {
  campaign: CampaignDetails;
  currency: string;
}

export function SummaryCards({ campaign, currency }: SummaryCardsProps) {
  return (
    <>
      {/* KPI Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(campaign.totalSpend, currency)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {campaign.startDate && campaign.endDate && (
                `For period ${formatDate(campaign.startDate)} - ${formatDate(campaign.endDate)}`
              )}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Impressions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(campaign.impressions)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clicks</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(campaign.clicks)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              CTR: {campaign.ctr || "N/A"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(campaign.conversions)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Value: {formatCurrency(campaign.totalConversionValue, currency)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">ROAS</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {campaign.roas ? `${campaign.roas.toFixed(2)}x` : "N/A"}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Return on Ad Spend
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">CPA</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {campaign.cpa || "N/A"}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Cost Per Acquisition
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">CPC</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {campaign.cpc || "N/A"}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Cost Per Click
            </p>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
