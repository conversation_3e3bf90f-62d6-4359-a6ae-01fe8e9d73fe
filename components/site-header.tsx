"use client"

import { ChevronRight } from "lucide-react"
import Link from "next/link"
import { Separator } from "./ui/separator"
import { SidebarTrigger } from "./ui/sidebar"
import { usePathname } from "next/navigation"

interface SiteHeaderProps {
  currentPath?: string;
  breadcrumbRootName?: string;
  breadcrumbRootPath?: string;
}

export function SiteHeader({
  currentPath,
  breadcrumbRootName = 'Dashboard',
  breadcrumbRootPath = '/'
}: SiteHeaderProps) {
  const hookPathname = usePathname(); // Call hook unconditionally
  const actualPathname = currentPath || hookPathname || '/';
  
  // Generate breadcrumbs from pathname
  const generateBreadcrumbs = () => {
    const paths = actualPathname.split('/').filter(Boolean);
    
    // Adjust initial breadcrumb based on root path
    const initialBreadcrumb = { name: breadcrumbRootName, path: breadcrumbRootPath };
    let breadcrumbPaths = paths;

    // If the actualPathname starts with breadcrumb<PERSON>ootPath (and it's not just '/'),
    // we need to remove that part from paths for further processing.
    if (breadcrumbRootPath !== '/' && actualPathname.startsWith(breadcrumbRootPath)) {
      const rootPathSegments = breadcrumbRootPath.split('/').filter(Boolean);
      breadcrumbPaths = paths.slice(rootPathSegments.length);
    } else if (breadcrumbRootPath === '/' && actualPathname === '/') {
       // Handle case where root is '/' and current path is also '/'
       return [initialBreadcrumb];
    } else if (breadcrumbRootPath !== '/' && actualPathname === breadcrumbRootPath) {
      // Handle case where current path is exactly the root path (not '/')
      return [initialBreadcrumb];
    }


    if (breadcrumbPaths.length === 0 && actualPathname !== breadcrumbRootPath) {
      // This case might occur if currentPath is like '/admin' and breadcrumbRootPath is '/admin'
      // and paths becomes empty after slicing.
      return [initialBreadcrumb];
    }
    
    return [
      initialBreadcrumb,
      ...breadcrumbPaths.map((path, index) => {
        // Construct URL relative to the breadcrumbRootPath if it's not '/'
        const basePath = breadcrumbRootPath === '/' ? '' : breadcrumbRootPath;
        const url = `${basePath}/${breadcrumbPaths.slice(0, index + 1).join('/')}`;
        // Format the path name to be more readable
        const name = path
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')
        
        return { name, path: url }
      })
    ]
  }
  
  const breadcrumbs = generateBreadcrumbs()
  
  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-3 sm:px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-1 sm:mx-2 data-[orientation=vertical]:h-4"
        />
        
        {/* Breadcrumbs */}
        <nav className="flex items-center text-sm min-w-0 flex-1">
          {breadcrumbs.map((crumb, index) => (
            <div key={crumb.path} className="flex items-center min-w-0">
              {index > 0 && <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 mx-1 text-muted-foreground flex-shrink-0" />}
              <Link 
                href={crumb.path}
                className={`${index === breadcrumbs.length - 1 
                  ? "font-medium text-foreground" 
                  : "text-muted-foreground hover:text-foreground"
                } truncate ${index === breadcrumbs.length - 1 ? '' : 'hidden sm:block'}`}
                title={crumb.name}
              >
                {crumb.name}
              </Link>
            </div>
          ))}
        </nav>
      </div>
    </header>
  )
}
