import { PoolClient } from "pg";
import { getRedshiftPool } from "@/lib/api/redshift";

// Define a type for CampaignDetails
export interface CampaignDetails {
  id: string; // campaign_name from API
  name: string; // campaign_name from API
  brand_name?: string;
  status?: string; // May not be available from current API
  budget?: number | null; // May not be available from current API
  spend: number | null; // Mapped from totalSpend
  impressions?: number | null;
  clicks?: number | null;
  ctr?: string | null; // May not be directly available from API for single campaign
  cpc?: string | null; // May not be directly available from API for single campaign
  conversions?: number | null;
  cpa?: string | null; // Provided by API
  roas?: number | null; // Provided by API
  startDate?: string | null;
  endDate?: string | null;
  targetAudience?: string | null; // May not be available from current API
  channels?: string[] | null;
  notes?: string | null; // May not be available from current API

  // Platform specific spends (optional)
  FacebookSpend?: number | null;
  InstagramSpend?: number | null;
  GoogleSpend?: number | null;
  TikTokSpend?: number | null;
  AmazonSpend?: number | null;
  // Campaign Type spends (optional)
  AwarenessSpend?: number | null;
  ConversionSpend?: number | null;
  RetargetingSpend?: number | null;
  SeasonalSpend?: number | null;
  totalConversionValue?: number | null;
  totalSpend?: number | null;
  
  // Daily metrics data (optional)
  dailyData?: Array<any>;
}

// Define internal interface for Redshift result rows
interface RedshiftRow {
  campaign_name: string | null;
  campaign_brand_id: number | null;
  brand_name: string | null;
  sales_channel_type: string | null;
  totalspend: number | null;
  facebookspend: number | null;
  instagramspend: number | null;
  googlespend: number | null;
  tiktokspend: number | null;
  amazonspend: number | null;
  awarenessspend: number | null;
  conversionspend: number | null;
  retargetingspend: number | null;
  seasonalspend: number | null;
  totalimpressions: number | null;
  totalclicks: number | null;
  totalconversions: number | null;
  totalconversionvalue: number | null;
  roas: number | null;
  cpa: number | null;
  min_date?: string;
  max_date?: string;
  platform_array?: string;
}

// Extract campaign name from a slug created by the campaign-data-table.tsx component
export function extractCampaignNameFromSlug(slug: string): string {
  // Our slug format is: lowercase-name-with-dashes-hash
  // First, we need to detect and remove the hash at the end (format: -xxxx)
  const hashPattern = /-[a-z0-9]{1,4}$/;
  
  // Remove the hash from the end
  const nameWithoutHash = slug.replace(hashPattern, '');
  
  // For additional robustness, attempt to recreate a reasonable campaign name
  // by converting dashes to spaces and proper casing words
  const words = nameWithoutHash.split('-');
  const properCaseName = words
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
  
  return properCaseName;
}

export async function getCampaignDetails(campaignId: string): Promise<CampaignDetails> {
  console.log(`[DEBUG] getCampaignDetails received campaignId (slug): '${campaignId}'`);

  // Process the campaign slug to extract a reasonable campaign name
  let decodedCampaignIdForQuery;
  try {
    // First decode any URL encoding
    const decodedSlug = decodeURIComponent(campaignId);
    // Then extract a proper name from the slug
    decodedCampaignIdForQuery = extractCampaignNameFromSlug(decodedSlug);
  } catch (e) {
    console.warn(`[DEBUG] Failed to process campaignId '${campaignId}' in getCampaignDetails. Using as is. Error:`, e);
    decodedCampaignIdForQuery = campaignId; // Fallback to using it as is if decoding fails
  }
  
  console.log(`[DEBUG] Processed campaign name for query: '${decodedCampaignIdForQuery}'`);
  const campaignIdForQuery = decodedCampaignIdForQuery;

  // Get the current date for default date range (last 30 days)
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30); // 30 days ago
  
  // Format dates as YYYY-MM-DD
  const formatDate = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  let client: PoolClient | null = null;
  
  try {
    // Get the Redshift pool
    const pool = getRedshiftPool();
    if (!pool) {
      throw new Error('Redshift connection pool is not available');
    }
    
    // Acquire a client from the pool
    client = await pool.connect();
    
    // Define the SQL query for a single campaign
    // We need to manually create a query similar to what the API service would build
    const exchangeRate = 1.34; // Using standard USD to CAD conversion rate
    
    // Define cost expression with inline rate
    const costExpression = `CASE WHEN m.currency = 'USD' THEN m.total_cost * ${exchangeRate} 
        WHEN m.currency = 'CAD' OR m.currency IS NULL THEN m.total_cost 
        ELSE m.total_cost END`;
    
    // Build the query
    const query = `
      SELECT
        m.campaign_name AS "campaign_name",
        m.brand AS "brand_name",
        b.brand_id AS "campaign_brand_id",
        MIN(m.date)::DATE::VARCHAR AS "min_date",
        MAX(m.date)::DATE::VARCHAR AS "max_date",
        MAX(m.advertising_platform) AS "platform_array",
        SUM(${costExpression}) AS "totalSpend",
        SUM(CASE WHEN m.advertising_platform ILIKE 'Facebook%' OR (m.advertising_platform ILIKE 'Meta%' AND (m.campaign_name ILIKE '%facebook%' OR m.advertising_group_name ILIKE '%facebook%')) THEN ${costExpression} ELSE 0 END) AS "FacebookSpend",
        SUM(CASE WHEN m.advertising_platform ILIKE 'Instagram%' OR (m.advertising_platform ILIKE 'Meta%' AND (m.campaign_name ILIKE '%instagram%' OR m.advertising_group_name ILIKE '%instagram%')) THEN ${costExpression} ELSE 0 END) AS "InstagramSpend",
        SUM(CASE WHEN m.advertising_platform ILIKE 'Google%' OR m.advertising_platform ILIKE 'Google Ads%' THEN ${costExpression} ELSE 0 END) AS "GoogleSpend",
        SUM(CASE WHEN m.advertising_platform ILIKE 'TikTok%' THEN ${costExpression} ELSE 0 END) AS "TikTokSpend",
        SUM(CASE WHEN m.advertising_platform ILIKE 'Amazon%' OR m.advertising_platform ILIKE 'Amazon Advertising%' THEN ${costExpression} ELSE 0 END) AS "AmazonSpend",
        SUM(CASE WHEN m.campaign_type ILIKE 'Awareness%' THEN ${costExpression} ELSE 0 END) AS "AwarenessSpend",
        SUM(CASE WHEN m.campaign_type ILIKE 'Conversion%' THEN ${costExpression} ELSE 0 END) AS "ConversionSpend",
        SUM(CASE WHEN m.campaign_type ILIKE 'Retargeting%' THEN ${costExpression} ELSE 0 END) AS "RetargetingSpend",
        SUM(CASE WHEN m.campaign_type ILIKE 'Seasonal%' THEN ${costExpression} ELSE 0 END) AS "SeasonalSpend",
        SUM(m.total_impressions) AS "totalImpressions",
        SUM(m.total_clicks) AS "totalClicks",
        SUM(m.number_of_sales_orders) AS "totalConversions",
        SUM(m.total_sales) AS "totalConversionValue",
        SUM(m.total_sales) / NULLIF(SUM(${costExpression}), 0) AS "roas",
        SUM(${costExpression}) / NULLIF(SUM(m.number_of_sales_orders), 0) AS "cpa"
      FROM dwh_ai.ai_reporting_ds_marketing_advertisings m
      LEFT JOIN dwh_ai.ai_reporting_brands b ON LOWER(m.brand) = LOWER(b.name)
      WHERE m.campaign_name = $1
        AND m.date >= $2
        AND m.date <= $3
      GROUP BY m.campaign_name, m.brand, b.brand_id
      ORDER BY "campaign_name";
    `;
    
    // Define query parameters
    const params = [
      campaignIdForQuery,
      formatDate(startDate),
      formatDate(endDate)
    ];
    
    console.log(`[DEBUG] Executing query: ${query}`);
    console.log(`[DEBUG] Query params: ${params}`);
    
    // Execute the query
    if (!client) {
      throw new Error('Failed to acquire database client');
    }
    
    const result = await client.query<RedshiftRow>(query, params);
    
    if (result.rows.length === 0) {
      throw new Error(`Campaign with ID ${campaignIdForQuery} not found or no data available.`);
    }
    
    // Transform the row data into the expected CampaignDetails format
    const row = result.rows[0];
    const campaignData: CampaignDetails = {
      id: row.campaign_name || campaignIdForQuery,
      name: row.campaign_name || campaignIdForQuery,
      brand_name: row.brand_name || undefined,
      spend: row.totalspend,
      impressions: row.totalimpressions,
      clicks: row.totalclicks,
      conversions: row.totalconversions,
      roas: row.roas,
      cpa: row.cpa ? parseFloat(row.cpa.toString()).toString() : null,
      startDate: row.min_date,
      endDate: row.max_date,
      channels: row.platform_array ? [row.platform_array] : null,
      FacebookSpend: row.facebookspend,
      InstagramSpend: row.instagramspend,
      GoogleSpend: row.googlespend,
      TikTokSpend: row.tiktokspend,
      AmazonSpend: row.amazonspend,
      AwarenessSpend: row.awarenessspend,
      ConversionSpend: row.conversionspend,
      RetargetingSpend: row.retargetingspend,
      SeasonalSpend: row.seasonalspend,
      totalConversionValue: row.totalconversionvalue,
      totalSpend: row.totalspend
    };
    
    // Calculate derived metrics if necessary
    if (campaignData.clicks && campaignData.impressions && campaignData.impressions > 0) {
      campaignData.ctr = ((campaignData.clicks / campaignData.impressions) * 100).toFixed(2) + '%';
    }
    
    if (campaignData.spend && campaignData.clicks && campaignData.clicks > 0) {
      campaignData.cpc = '$' + (campaignData.spend / campaignData.clicks).toFixed(2);
    }
    
    console.log("[DEBUG] Successfully fetched and processed campaign data directly from DB");
    return campaignData;
    
  } catch (error) {
    console.error('[DEBUG] Error in direct database query:', error);
    throw error;
  } finally {
    if (client) {
      client.release();
      console.log('[DEBUG] Database client released');
    }
  }
}
