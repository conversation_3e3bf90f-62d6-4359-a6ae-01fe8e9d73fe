import { Pool, PoolClient } from 'pg'; // Import PoolClient

let pool: Pool | null = null;

/**
 * Reset the pool instance (for testing purposes)
 */
export function resetPool(): void {
  if (pool) {
    pool.end();
  }
  pool = null;
}

/**
 * Get or create the Redshift connection pool
 *
 * This function ensures that the pool is created only once and only if all required
 * environment variables are present. It allows for lazy initialization of the pool,
 * which is useful for testing environments where environment variables may be loaded
 * after module imports.
 *
 * @returns The Redshift connection pool, or null if it couldn't be created
 */
export function getRedshiftPool(): Pool | null {
  // If pool already exists, return it
  if (pool) {
    return pool;
  }
  
  // Get environment variables at the time of pool creation
  const {
    REDSHIFT_HOST,
    REDSHIFT_PORT,
    REDSHIFT_DATABASE,
    REDSHIFT_USER,
    REDSHIFT_PASSWORD,
  } = process.env;
  
  // Check if all required environment variables are present
  const missingVars = [];
  if (!REDSHIFT_HOST) missingVars.push('REDSHIFT_HOST');
  if (!REDSHIFT_PORT) missingVars.push('REDSHIFT_PORT');
  if (!REDSHIFT_DATABASE) missingVars.push('REDSHIFT_DATABASE');
  if (!REDSHIFT_USER) missingVars.push('REDSHIFT_USER');
  if (!REDSHIFT_PASSWORD) missingVars.push('REDSHIFT_PASSWORD');
  
  // Only create the pool if all required variables are present
  if (missingVars.length > 0) {
    console.error(`Missing Redshift environment variables: ${missingVars.join(', ')}`);
    console.warn('Redshift connection will not be available for testing');
    // Leave pool as null to indicate Redshift is not available
    return null;
  }
  
  try {
    console.log('Creating Redshift connection pool with config:', {
      host: REDSHIFT_HOST,
      port: parseInt(REDSHIFT_PORT || '5439', 10),
      database: REDSHIFT_DATABASE,
      user: 'REDSHIFT_USER is set',
      password: 'REDSHIFT_PASSWORD is set',
      ssl: true // Logging the current SSL setting
    });
    
    pool = new Pool({
      host: REDSHIFT_HOST,
      port: parseInt(REDSHIFT_PORT || '5439', 10), // Default Redshift port
      database: REDSHIFT_DATABASE,
      user: REDSHIFT_USER,
      password: REDSHIFT_PASSWORD,
      // Redshift often requires SSL, adjust as needed for your configuration
      ssl: {
        rejectUnauthorized: false // Enable SSL with rejectUnauthorized: false
      },
      max: 10, // Max number of clients in the pool
      idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
      connectionTimeoutMillis: 5000, // Increased timeout for connection attempts
    });
    
    // Add error handler to the pool
    pool.on('error', (err: Error) => {
      console.error('Unexpected error on idle Redshift client', err);
      // Optionally, you could try to terminate the client or take other actions
    });
    
    console.log('Redshift connection pool created successfully.');
    return pool;
    
  } catch (error) {
    console.error('Failed to create Redshift connection pool:', error);
    // Handle pool creation failure (e.g., log, exit, notify)
    pool = null; // Ensure pool is null if creation fails
    return null;
  }
}

// For backward compatibility, export the pool getter as a property
// This ensures existing code that uses redshiftPool directly will still work
Object.defineProperty(exports, 'redshiftPool', {
  get: function() {
    return getRedshiftPool();
  }
});

// Optional: Function to test the connection
export async function testRedshiftConnection() {
  const redshiftPool = getRedshiftPool();
  
  if (!redshiftPool) {
    console.error('Redshift pool is not available.');
    return false;
  }
  
  let client: PoolClient | undefined; // Use PoolClient type
  try {
    client = await redshiftPool.connect();
    console.log('Successfully connected to Redshift for testing.');
    await client.query('SELECT 1'); // Simple query to test connection
    return true;
  } catch (error) {
    console.error('Redshift connection test failed:', error);
    return false;
  } finally {
    // Use explicit check before releasing client
    if (client) {
      client.release();
    }
  }
}
