import type { Session } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { getServerSession } from 'next-auth';

/**
 * Safely get server session with error handling for unauthenticated users
 * Returns null if JWT decoding fails or user is not authenticated
 */
export async function getSafeServerSession(): Promise<Session | null> {
  try {
    const session = await getServerSession(authOptions);
    return session;
  } catch (error) {
    console.error('[Auth Helper] Error getting server session:', error);

    // Handle JWT decoding errors gracefully
    if (error instanceof Error) {
      const errorMessage = error.message;
      const errorName = error.name;

      // Log specific JWT errors without exposing sensitive information (case-insensitive)
      const lowerErrorName = errorName.toLowerCase();
      const lowerErrorMessage = errorMessage.toLowerCase();

      if (lowerErrorName.includes('jwe') || lowerErrorName.includes('jwt') || lowerErrorMessage.includes('decrypt')) {
        console.log('[Auth Helper] JWT decoding failed for unauthenticated user, returning null');
        return null;
      }

      // Log other authentication errors
      console.error('[Auth Helper] Authentication error:', {
        name: errorName,
        message: errorMessage.includes('JWE') ? 'Authentication failed' : errorMessage
      });
    }

    // Return null for any authentication errors to prevent crashes
    return null;
  }
}

/**
 * Check if user is authenticated without triggering database operations
 * Useful for pages that need to conditionally render based on auth status
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    const session = await getSafeServerSession();
    return !!session?.user?.id;
  } catch (error) {
    console.error('[Auth Helper] Error checking authentication status:', error);
    return false;
  }
}