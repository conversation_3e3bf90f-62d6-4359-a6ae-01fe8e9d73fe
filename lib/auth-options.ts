import { Account, User as NextAuthUser, Profile, Session } from "next-auth";

import GoogleProvider from "next-auth/providers/google";
import { JWT } from "next-auth/jwt";
import type { NextAuthOptions } from "next-auth";
import { getDb } from "@/lib/api/db";

// Configure NextAuth options
export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
      // Add profile callback to debug and ensure user creation
      profile(profile: Profile & { sub?: string; picture?: string }) {
        console.log('[GoogleProvider] Raw profile:', JSON.stringify(profile, null, 2));
        return {
          id: profile.sub ?? '',
          name: profile.name,
          email: profile.email,
          image: profile.picture,
        };
      },
    }),
  ],
  // Customize pages
  pages: {
    signIn: "/auth/signin",
  },
  // Callbacks
  callbacks: {
    async jwt({ token, user, trigger, session: updateData }: { token: JWT, user?: NextAuthUser, account?: Account | null, profile?: Profile, isNewUser?: boolean, trigger?: "signIn" | "signUp" | "update", session?: Record<string, unknown> }): Promise<JWT> {
      console.log('[NextAuth JWT] Callback start. Trigger:', trigger, 'User:', !!user);
      
      // Type assertion for currentToken to use augmented JWT properties
      let currentToken: JWT = { ...token };

      // If trigger is "update" (e.g., client calls session.update()), merge updateData into currentToken
      if (trigger === "update" && updateData) {
        console.log('[NextAuth JWT] Trigger is "update", merging updateData into token.');
        currentToken = { ...currentToken, ...updateData };
      }

      // Handle impersonation logic (minimal operations only)
      if (currentToken.impersonateTargetUserId && currentToken.roles && currentToken.roles.includes('Super Admin')) {
        console.log(`[NextAuth JWT] Starting impersonation for user ID: ${currentToken.impersonateTargetUserId}`);
        if (!currentToken.isImpersonating) {
          currentToken.originalUser = {
            id: currentToken.sub || '',
            name: currentToken.name,
            email: currentToken.email,
            roles: currentToken.roles ? [...currentToken.roles] : [],
            permissions: currentToken.permissions ? [...currentToken.permissions] : [],
          };
        }
        currentToken.sub = currentToken.impersonateTargetUserId;
        currentToken.isImpersonating = true;
        delete currentToken.impersonateTargetUserId;
      }
      // Check if reverting impersonation
      else if (currentToken.revertImpersonation && currentToken.isImpersonating && currentToken.originalUser) {
        console.log('[NextAuth JWT] Reverting impersonation.');
        const original = currentToken.originalUser;
        currentToken.sub = original.id;
        currentToken.name = original.name;
        currentToken.email = original.email;
        currentToken.roles = original.roles ? [...original.roles] : [];
        currentToken.permissions = original.permissions ? [...original.permissions] : [];
        
        delete currentToken.isImpersonating;
        delete currentToken.originalUser;
        delete currentToken.revertImpersonation;
      }

      // Only handle initial user creation/lookup during sign-in
      if (user && !currentToken.isImpersonating) {
        console.log('[NextAuth JWT] Processing initial user object (OAuth sign-in)');
        
        // Set basic user info from OAuth
        currentToken.sub = user.id;
        currentToken.name = user.name;
        currentToken.email = user.email;
        
        // Mark for database operations in session callback
        currentToken.needsDbSync = true;
      }

      // Designate super admin (minimal check)
      if (currentToken.email === '<EMAIL>') {
        currentToken.isSuperAdmin = true;
      } else {
        currentToken.isSuperAdmin = false;
      }

      console.log('[NextAuth JWT] JWT callback completed');
      return currentToken;
    },
    async session({ session, token }: { session: Session, token: JWT }): Promise<Session> {
      console.log('[NextAuth Session] Callback start');
      console.log('[NextAuth Session] Initial token data:', {
        sub: token.sub,
        email: token.email,
        isSuperAdmin: token.isSuperAdmin,
        needsDbSync: token.needsDbSync,
        isImpersonating: token.isImpersonating,
        hasRoles: !!token.roles,
        rolesCount: token.roles?.length || 0
      });
      
      if (!session.user || !token.sub) {
        console.log('[NextAuth Session] Missing session.user or token.sub, returning session as-is');
        console.log('[NextAuth Session] Session user exists:', !!session.user);
        console.log('[NextAuth Session] Token sub exists:', !!token.sub);
        return session;
      }

      try {
        // Basic user info assignment
        session.user.id = token.sub;
        session.user.name = token.name;
        session.user.email = token.email;
        session.user.isSuperAdmin = token.isSuperAdmin;
        session.user.isImpersonating = token.isImpersonating;
        session.user.originalUser = token.originalUser;

        // Handle database operations for user data fetching
        // CRITICAL FIX: Always sync for super admin to ensure permissions are loaded
        const shouldSyncDb = token.needsDbSync || token.isImpersonating || !token.roles ||
                           (token.email === '<EMAIL>' && (!token.roles || token.roles.length === 0));
        
        if (shouldSyncDb) {
          console.log('[NextAuth Session] Performing database sync for user data');
          console.log('[NextAuth Session] DB sync triggered by:', {
            needsDbSync: token.needsDbSync,
            isImpersonating: token.isImpersonating,
            hasRoles: !!token.roles,
            rolesLength: token.roles?.length || 0,
            userEmail: token.email,
            isSuperAdminEmail: token.email === '<EMAIL>'
          });
          
          const db = await getDb();
          let userId = token.sub;

          // Handle impersonation database lookup
          if (token.isImpersonating && token.impersonateTargetUserId) {
            const targetUserDb = await db.get<{ id: number; name: string; email: string } | undefined>(
              'SELECT id, name, email FROM Users WHERE id = ?',
              [token.impersonateTargetUserId]
            );
            
            if (targetUserDb) {
              userId = targetUserDb.id.toString();
              session.user.name = targetUserDb.name;
              session.user.email = targetUserDb.email;
            }
          }

          // Handle new user creation if needed
          if (token.needsDbSync && !token.isImpersonating) {
            const dbUser = await db.get<{ id: number } | undefined>('SELECT id FROM Users WHERE email = ?', [token.email]);
            
            if (!dbUser && token.email) {
              console.log(`[NextAuth Session] Creating new user for ${token.email}`);
              const nameToInsert = token.name || 'Default User';
              const passwordHash = 'oauth-user-no-password';
              const result = await db.run('INSERT INTO Users (email, name, password_hash) VALUES (?, ?, ?)', [token.email, nameToInsert, passwordHash]);
              
              if (result.lastID) {
                userId = result.lastID.toString();
                await db.run('UPDATE Users SET lastLoginAt = ? WHERE id = ?', [new Date().toISOString(), userId]);
                
                if (token.email === '<EMAIL>') {
                  const superAdminRole = await db.get<{ id: number } | undefined>("SELECT id FROM Roles WHERE name = 'Super Admin'");
                  if (superAdminRole) await db.run('INSERT INTO UserRoles (user_id, role_id) VALUES (?, ?)', [userId, superAdminRole.id]);
                }
              }
            } else if (dbUser) {
              userId = dbUser.id.toString();
              await db.run('UPDATE Users SET lastLoginAt = ? WHERE id = ?', [new Date().toISOString(), userId]);
              
              // CRITICAL FIX: Enhanced super admin role assignment
              if (token.email === '<EMAIL>') {
                console.log('[DEBUG SESSION] Ensuring super admin <NAME_EMAIL>');
                try {
                  const rolesDb = await db.all<{ id: number, name: string }[]>('SELECT id, name FROM Roles');
                  console.log('[DEBUG SESSION] Available roles in database:', rolesDb);
                  
                  const superAdminRole = rolesDb.find((role: {name: string}) => role.name === 'Super Admin');
                  const adminRole = rolesDb.find((role: {name: string}) => role.name === 'Admin');
                  
                  if (superAdminRole) {
                    await db.run('INSERT OR IGNORE INTO UserRoles (user_id, role_id) VALUES (?, ?)', [userId, superAdminRole.id]);
                    console.log('[DEBUG SESSION] Super Admin role assigned/confirmed');
                  } else {
                    console.error('[DEBUG SESSION] Super Admin role not found in database!');
                  }
                  
                  if (adminRole) {
                    await db.run('INSERT OR IGNORE INTO UserRoles (user_id, role_id) VALUES (?, ?)', [userId, adminRole.id]);
                    console.log('[DEBUG SESSION] Admin role assigned/confirmed');
                  } else {
                    console.error('[DEBUG SESSION] Admin role not found in database!');
                  }
                } catch (roleError) {
                  console.error('[DEBUG SESSION] Error assigning super admin roles:', roleError);
                }
              }
            }
          }

          // Fetch user roles and permissions
          if (userId) {
            console.log('[DEBUG SESSION] Starting user data fetch for userId:', userId);
            
            try {
              // CRITICAL FIX: More robust role fetching with better error handling
              const userRoles = await db.all<{ role_id: number }[]>(
                `SELECT ur.role_id FROM UserRoles ur JOIN Roles r ON ur.role_id = r.id WHERE ur.user_id = ?`,
                [userId]
              );
              const roleIds = Array.isArray(userRoles) ? userRoles.map(ur => ur.role_id) : [];
              console.log('[DEBUG SESSION] User roles query result:', { userRoles, roleIds });

              if (roleIds.length > 0) {
                // Fetch role names
                const roles = await db.all<{ name: string }[]>(
                  `SELECT name FROM Roles WHERE id IN (${roleIds.map(() => '?').join(',')})`,
                  roleIds
                );
                session.user.roles = Array.isArray(roles) ? roles.map(r => r.name) : [];
                console.log('[DEBUG SESSION] User roles:', session.user.roles);

                // CRITICAL FIX: Enhanced permission fetching with fallback
                try {
                  const rolePermissions = await db.all<{ permission_name: string }[]>(
                    `SELECT (p.action || ':' || p.resource) as permission_name
                     FROM RolePermissions rp
                     JOIN Permissions p ON rp.permission_id = p.id
                     WHERE rp.role_id IN (${roleIds.map(() => '?').join(',')})`,
                    roleIds
                  );
                  session.user.permissions = Array.isArray(rolePermissions) ?
                    [...new Set(rolePermissions.map(rp => rp.permission_name))] : [];
                  console.log('[DEBUG SESSION] User permissions from DB:', session.user.permissions);
                  
                  // CRITICAL FIX: Ensure super admin has all permissions even if DB query succeeds but returns incomplete data
                  if (token.email === '<EMAIL>' && session.user.permissions.length < 10) {
                    console.log('[DEBUG SESSION] Super admin has incomplete permissions, applying full set');
                    session.user.permissions = ['view_users', 'manage_users', 'view_roles', 'manage_roles', 'view_permissions', 'manage_permissions', 'view_groups', 'manage_groups', 'view_brands', 'manage_brands'];
                  }
                } catch (permError) {
                  console.error('[NextAuth Session] Error fetching permissions:', permError);
                  console.log('[DEBUG SESSION] Permission fetch failed, applying fallback logic');
                  session.user.permissions = [];
                  if (token.email === '<EMAIL>') {
                    console.log('[DEBUG SESSION] CRITICAL: Applying super admin permission fallback due to DB error');
                    session.user.permissions = ['view_users', 'manage_users', 'view_roles', 'manage_roles', 'view_permissions', 'manage_permissions', 'view_groups', 'manage_groups', 'view_brands', 'manage_brands'];
                    console.log('[DEBUG SESSION] Super admin permission fallback applied:', session.user.permissions);
                  }
                }
              } else {
                console.log('[DEBUG SESSION] No roles found for user, applying fallback logic');
                session.user.roles = [];
                session.user.permissions = [];
                
                // CRITICAL FIX: Always apply super admin fallback for the designated email
                if (token.email === '<EMAIL>') {
                  console.log('[DEBUG SESSION] CRITICAL: Applying super admin <NAME_EMAIL>');
                  session.user.permissions = ['view_users', 'manage_users', 'view_roles', 'manage_roles', 'view_permissions', 'manage_permissions', 'view_groups', 'manage_groups', 'view_brands', 'manage_brands'];
                  session.user.roles = ['Admin', 'Super Admin'];
                  console.log('[DEBUG SESSION] Super admin fallback applied - roles:', session.user.roles);
                  console.log('[DEBUG SESSION] Super admin fallback applied - permissions:', session.user.permissions);
                }
              }
            } catch (roleError) {
              console.error('[DEBUG SESSION] Error fetching user roles:', roleError);
              // CRITICAL FIX: Comprehensive fallback for any database errors
              session.user.roles = [];
              session.user.permissions = [];
              
              if (token.email === '<EMAIL>') {
                console.log('[DEBUG SESSION] CRITICAL: Database error, applying super admin emergency fallback');
                session.user.permissions = ['view_users', 'manage_users', 'view_roles', 'manage_roles', 'view_permissions', 'manage_permissions', 'view_groups', 'manage_groups', 'view_brands', 'manage_brands'];
                session.user.roles = ['Admin', 'Super Admin'];
                console.log('[DEBUG SESSION] Emergency super admin fallback applied');
              }
            }

            // Fetch user groups
            const userGroups = await db.all<{ group_id: number }[]>(`SELECT group_id FROM UserGroups WHERE user_id = ?`, [userId]);
            session.user.groups = Array.isArray(userGroups) ? userGroups.map(ug => ug.group_id) : [];
            console.log('[DEBUG SESSION] User groups:', session.user.groups);

            // Fetch user brands
            const userBrands = await db.all<{ brand_id: number }[]>(`SELECT brand_id FROM UserBrands WHERE user_id = ?`, [userId]);
            session.user.brands = Array.isArray(userBrands) ? userBrands.map(ub => ub.brand_id) : [];
            console.log('[DEBUG SESSION] Direct user brands:', session.user.brands);

            // Augment brands with those accessible via user's groups
            if (session.user.groups && session.user.groups.length > 0) {
              const groupBrandIds = await db.all<{ brand_id: number }[]>(
                `SELECT DISTINCT brand_id FROM BrandGroups WHERE group_id IN (${session.user.groups.map(() => '?').join(',')})`,
                session.user.groups
              );
              console.log('[DEBUG SESSION] Group brand IDs query result:', groupBrandIds);
              if (Array.isArray(groupBrandIds)) {
                const brandsFromGroups = groupBrandIds.map(gb => gb.brand_id);
                session.user.brands = [...new Set([...(session.user.brands || []), ...brandsFromGroups])];
                console.log('[DEBUG SESSION] Final user brands (direct + group):', session.user.brands);
              }
            }
          } else {
            session.user.roles = [];
            session.user.permissions = [];
            session.user.groups = [];
            session.user.brands = [];
          }
        } else {
          // Use cached data from token
          session.user.roles = token.roles || [];
          session.user.permissions = token.permissions || [];
          session.user.groups = token.groups || [];
          session.user.brands = token.brands || [];
        }

        // CRITICAL FIX: Always update the token with the fetched user data
        // This ensures the JWT token contains the user's permissions for API calls
        token.roles = session.user.roles || [];
        token.permissions = session.user.permissions || [];
        token.groups = session.user.groups || [];
        token.brands = session.user.brands || [];
        
        // CRITICAL FIX: Clear needsDbSync flag to prevent unnecessary re-syncing
        if (token.needsDbSync) {
          delete token.needsDbSync;
        }
        
        console.log('[DEBUG SESSION] Updated JWT token with user data:', {
          roles: token.roles,
          rolesCount: token.roles?.length || 0,
          permissions: token.permissions?.length || 0,
          groups: token.groups?.length || 0,
          brands: token.brands?.length || 0,
          clearedNeedsDbSync: true
        });

        // Set top-level session properties for UI
        session.isImpersonating = token.isImpersonating;
        if (token.originalUser) {
          session.originalUserId = token.originalUser.id;
        } else {
          delete session.originalUserId;
        }

        console.log('[NextAuth Session] Session callback completed successfully');
        console.log('[DEBUG SESSION] Final session data for user:', {
          email: session.user.email,
          roles: session.user.roles,
          permissions: session.user.permissions?.length,
          hasAdminRole: session.user.roles?.includes('Admin'),
          hasSuperAdminRole: session.user.roles?.includes('Super Admin'),
          hasViewGroupsPermission: session.user.permissions?.includes('view_groups'),
          hasManageGroupsPermission: session.user.permissions?.includes('manage_groups')
        });
      } catch (error) {
        console.error('[NextAuth Session] Error during session callback:', error);
        // Provide fallback values to prevent crashes
        session.user.roles = [];
        session.user.permissions = [];
        session.user.groups = [];
        session.user.brands = [];
        session.user.isImpersonating = false;
        delete session.user.originalUser;
        delete session.isImpersonating;
        delete session.originalUserId;
      }

      return session;
    },
  },
  debug: process.env.NODE_ENV === "development",
  secret: process.env.NEXTAUTH_SECRET,
  jwt: {
    secret: process.env.NEXTAUTH_SECRET,
    // Add diagnostic logging for JWT operations
    encode: async ({ secret, token, maxAge }) => {
      console.log('[NextAuth JWT Encode] Starting JWT encoding');
      console.log('[NextAuth JWT Encode] Secret available:', !!secret);
      console.log('[NextAuth JWT Encode] Token keys:', Object.keys(token || {}));
      console.log('[NextAuth JWT Encode] User data in token:', {
        hasRoles: !!(token?.roles),
        rolesCount: token?.roles?.length || 0,
        hasPermissions: !!(token?.permissions),
        permissionsCount: token?.permissions?.length || 0,
        hasGroups: !!(token?.groups),
        groupsCount: token?.groups?.length || 0,
        hasBrands: !!(token?.brands),
        brandsCount: token?.brands?.length || 0
      });
      try {
        const { encode } = await import('next-auth/jwt');
        const result = await encode({ secret, token, maxAge });
        console.log('[NextAuth JWT Encode] Successfully encoded JWT with user data');
        return result;
      } catch (error) {
        console.error('[NextAuth JWT Encode] Error encoding JWT:', error);
        throw error;
      }
    },
    decode: async ({ secret, token }) => {
      console.log('[NextAuth JWT Decode] Starting JWT decoding');
      console.log('[NextAuth JWT Decode] Secret available:', !!secret);
      console.log('[NextAuth JWT Decode] Token available:', !!token);
      
      // Return null for missing or invalid tokens to prevent errors
      if (!token || !secret) {
        console.log('[NextAuth JWT Decode] Missing token or secret, returning null');
        return null;
      }
      
      try {
        const { decode } = await import('next-auth/jwt');
        const result = await decode({ secret, token });
        console.log('[NextAuth JWT Decode] Successfully decoded JWT');
        return result;
      } catch (error) {
        console.error('[NextAuth JWT Decode] Error decoding JWT:', error);
        
        // Handle specific JWT errors gracefully
        if (error instanceof Error) {
          const errorName = error.name;
          const errorMessage = error.message;
          
          // Log error details for debugging but don't expose sensitive info
          console.error('[NextAuth JWT Decode] Error details:', {
            name: errorName,
            message: errorMessage.includes('JWE') ? 'JWT decoding failed' : errorMessage
          });
          
          // For JWT decoding errors, return null instead of throwing
          if (errorName.includes('JWE') || errorName.includes('JWT') || errorMessage.includes('decrypt')) {
            console.log('[NextAuth JWT Decode] JWT decoding failed, returning null for graceful handling');
            return null;
          }
        }
        
        // For other errors, still throw to maintain error handling
        throw error;
      }
    },
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
};
