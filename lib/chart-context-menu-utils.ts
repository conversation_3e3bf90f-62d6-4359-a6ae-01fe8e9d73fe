import { formatKpiValue } from 'lib/chart-utils';

// Type definitions for chart context menu functionality
export interface ChartContextMenuData {
  date: string;
  [kpiName: string]: string | number | null;
}

export interface ChartElementData {
  date: string;
  kpiName: string;
  value: string | number | null;
  formattedValue: string;
  rawValue: string | number | null;
}

export interface ChartContextMenuOptions {
  currency?: string;
  includeHeaders?: boolean;
  dateFormat?: 'default' | 'iso';
  valueFormat?: 'formatted' | 'raw' | 'both';
}

export interface ClipboardResult {
  success: boolean;
  error?: string;
}

/**
 * Extract data for a specific chart element (bar, point, etc.)
 * @param chartData - The full chart dataset
 * @param elementIndex - Index of the data point in the chart
 * @param kpiName - Name of the KPI/metric being extracted
 * @param options - Configuration options for data extraction
 * @returns ChartElementData object with formatted information
 */
export function extractChartElementData(
  chartData: ChartContextMenuData[],
  elementIndex: number,
  kpiName: string,
  options: ChartContextMenuOptions = {}
): ChartElementData | null {
  const { currency = 'CAD' } = options;

  if (!chartData || elementIndex < 0 || elementIndex >= chartData.length) {
    return null;
  }

  const dataPoint = chartData[elementIndex];
  if (!dataPoint) {
    return null;
  }

  const rawValue = dataPoint[kpiName];
  const formattedValue = typeof rawValue === 'number' 
    ? formatKpiValue(rawValue, kpiName, currency)
    : rawValue?.toString() || 'N/A';

  return {
    date: dataPoint.date,
    kpiName,
    value: rawValue,
    formattedValue,
    rawValue
  };
}

/**
 * Extract entire chart data as a formatted table structure
 * @param chartData - The full chart dataset
 * @param options - Configuration options for data extraction
 * @returns Array of arrays representing table rows with headers
 */
export function extractChartTableData(
  chartData: ChartContextMenuData[],
  options: ChartContextMenuOptions = {}
): string[][] {
  const { currency = 'CAD', includeHeaders = true, valueFormat = 'formatted' } = options;

  if (!chartData || chartData.length === 0) {
    return [];
  }

  // Get all unique KPI names (excluding 'date')
  const kpiNames = Array.from(
    new Set(
      chartData.flatMap(row => 
        Object.keys(row).filter(key => key !== 'date')
      )
    )
  ).sort();

  const tableData: string[][] = [];

  // Add headers if requested
  if (includeHeaders) {
    tableData.push(['Date', ...kpiNames]);
  }

  // Add data rows
  chartData.forEach(row => {
    const rowData: string[] = [row.date];
    
    kpiNames.forEach(kpiName => {
      const rawValue = row[kpiName];
      let cellValue: string;

      if (rawValue === null || rawValue === undefined) {
        cellValue = 'N/A';
      } else if (valueFormat === 'raw') {
        cellValue = rawValue.toString();
      } else if (valueFormat === 'both') {
        const formatted = typeof rawValue === 'number' 
          ? formatKpiValue(rawValue, kpiName, currency)
          : rawValue.toString();
        cellValue = `${formatted} (${rawValue})`;
      } else {
        // Default: formatted
        cellValue = typeof rawValue === 'number' 
          ? formatKpiValue(rawValue, kpiName, currency)
          : rawValue.toString();
      }

      rowData.push(cellValue);
    });

    tableData.push(rowData);
  });

  return tableData;
}

/**
 * Format data for clipboard copying (tab-separated values)
 * @param tableData - Array of arrays representing table data
 * @returns Tab-separated string ready for clipboard
 */
export function formatDataForClipboard(tableData: string[][]): string {
  if (!tableData || tableData.length === 0) {
    return '';
  }

  return tableData
    .map(row => row.join('\t'))
    .join('\n');
}

/**
 * Copy text to clipboard with error handling
 * @param text - Text to copy to clipboard
 * @returns Promise with success/error result
 */
export async function copyToClipboard(text: string): Promise<ClipboardResult> {
  try {
    // Check if clipboard API is available
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return { success: true };
    } else {
      // Fallback for older browsers or non-secure contexts
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (successful) {
        return { success: true };
      } else {
        return { 
          success: false, 
          error: 'Copy command failed' 
        };
      }
    }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Copy specific chart element data to clipboard
 * @param chartData - The full chart dataset
 * @param elementIndex - Index of the data point in the chart
 * @param kpiName - Name of the KPI/metric being copied
 * @param options - Configuration options
 * @returns Promise with success/error result
 */
export async function copyChartElementData(
  chartData: ChartContextMenuData[],
  elementIndex: number,
  kpiName: string,
  options: ChartContextMenuOptions = {}
): Promise<ClipboardResult> {
  const elementData = extractChartElementData(chartData, elementIndex, kpiName, options);
  
  if (!elementData) {
    return { 
      success: false, 
      error: 'No data found for the specified element' 
    };
  }

  const { valueFormat = 'formatted' } = options;
  let clipboardText: string;

  if (valueFormat === 'both') {
    clipboardText = `${elementData.kpiName}\nDate: ${elementData.date}\nValue: ${elementData.formattedValue} (${elementData.rawValue})`;
  } else if (valueFormat === 'raw') {
    clipboardText = `${elementData.kpiName}\nDate: ${elementData.date}\nValue: ${elementData.rawValue}`;
  } else {
    clipboardText = `${elementData.kpiName}\nDate: ${elementData.date}\nValue: ${elementData.formattedValue}`;
  }

  return await copyToClipboard(clipboardText);
}

/**
 * Copy entire chart data table to clipboard
 * @param chartData - The full chart dataset
 * @param options - Configuration options
 * @returns Promise with success/error result
 */
export async function copyChartTableData(
  chartData: ChartContextMenuData[],
  options: ChartContextMenuOptions = {}
): Promise<ClipboardResult> {
  const tableData = extractChartTableData(chartData, options);
  
  if (tableData.length === 0) {
    return { 
      success: false, 
      error: 'No data available to copy' 
    };
  }

  const clipboardText = formatDataForClipboard(tableData);
  return await copyToClipboard(clipboardText);
}

/**
 * Utility function to get available KPI names from chart data
 * @param chartData - The chart dataset
 * @returns Array of KPI names (excluding 'date')
 */
export function getAvailableKpiNames(chartData: ChartContextMenuData[]): string[] {
  if (!chartData || chartData.length === 0) {
    return [];
  }

  return Array.from(
    new Set(
      chartData.flatMap(row => 
        Object.keys(row).filter(key => key !== 'date')
      )
    )
  ).sort();
}

/**
 * Utility function to validate chart data structure
 * @param chartData - The chart dataset to validate
 * @returns Boolean indicating if data is valid
 */
export function isValidChartData(chartData: unknown): chartData is ChartContextMenuData[] {
  if (!Array.isArray(chartData) || chartData.length === 0) {
    return false;
  }

  return chartData.every(row => 
    typeof row === 'object' && 
    row !== null && 
    'date' in row && 
    typeof row.date === 'string'
  );
}

/**
 * Create a formatted summary of chart data for display
 * @param chartData - The chart dataset
 * @param options - Configuration options
 * @returns Formatted summary string
 */
export function createChartDataSummary(
  chartData: ChartContextMenuData[]
): string {
  if (!isValidChartData(chartData)) {
    return 'No valid chart data available';
  }

  const kpiNames = getAvailableKpiNames(chartData);
  const dateRange = chartData.length > 0 ? 
    `${chartData[0].date} to ${chartData[chartData.length - 1].date}` : 
    'No dates';

  return `Chart Data Summary:
- Date Range: ${dateRange}
- Data Points: ${chartData.length}
- Metrics: ${kpiNames.length} (${kpiNames.join(', ')})`;
}