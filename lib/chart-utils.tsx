// Chart colors used across the application
export const CHART_COLORS = {
  blue: "#2563EB", // Primary blue
  cyan: "#0EA5E9", // Light blue
  azure: "#0284C7", // Azure blue (for grid lines)
  purple: "#9333EA", // Purple
  pink: "#EC4899", // Pink
  orange: "#F97316", // Orange
  yellow: "#EAB308", // Yellow
  green: "#22C55E", // Green
  red: "#EF4444", // Red
  gray: "#6B7280", // Gray
  indigo: "#6366F1", // Indigo
  teal: "#14B8A6", // Teal
  amber: "#F59E0B", // Amber
  emerald: "#10B981", // Emerald
  lime: "#84CC16", // Lime
  rose: "#F43F5E", // Rose
};

// Format number with thousands separator
export function formatNumber(value: number | null | undefined, defaultValue = "N/A"): string {
  if (value === null || value === undefined) return defaultValue;
  return value.toLocaleString();
}

// Format currency with symbol and thousands separator
export function formatCurrency(
  value: number | null | undefined,
  currency = "CAD",
  defaultValue = "N/A"
): string {
  if (value === null || value === undefined) return defaultValue;

  const symbol = currency === "USD" ? "$" : currency === "CAD" ? "$" : "£";
  return `${symbol}${value.toLocaleString(undefined, { 
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2
  })}`;
}

// Format percentage with % symbol (converts decimal to percentage)
export function formatPercent(
  value: number | null | undefined,
  defaultValue = "N/A"
): string {
  if (value === null || value === undefined) return defaultValue;
  
  // Convert decimal to percentage (multiply by 100) and format with 2 decimal places
  return `${(value * 100).toFixed(2)}%`;
}

// Format raw percentage values (already in percentage format, don't multiply by 100)
export function formatRawPercent(
  value: number | null | undefined,
  defaultValue = "N/A"
): string {
  if (value === null || value === undefined) return defaultValue;
  
  // Value is already in percentage format, just format with 2 decimal places
  return `${value.toFixed(2)}%`;
}

// Format date to MM/DD/YYYY
export function formatDate(dateString: string, defaultValue = "N/A"): string {
  if (!dateString) return defaultValue;

  // Create a date object - this shouldn't throw errors in modern JavaScript
  const date = new Date(dateString);
  
  // Check if the date is valid
  if (isNaN(date.getTime())) {
    return dateString;
  }
  
  return date.toLocaleDateString();
}

// Generate a random color from the CHART_COLORS palette
export function getRandomColor(): string {
  const colors = Object.values(CHART_COLORS);
  return colors[Math.floor(Math.random() * colors.length)];
}

// Get contrasting text color for a given background color
export function getContrastTextColor(hexColor: string): string {
  // Convert hex to RGB
  const r = parseInt(hexColor.slice(1, 3), 16);
  const g = parseInt(hexColor.slice(3, 5), 16);
  const b = parseInt(hexColor.slice(5, 7), 16);
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // Return black for light colors, white for dark colors
  return luminance > 0.5 ? "#000000" : "#FFFFFF";
}

// Get color for brand by name and index
export function getBrandColor(brandName: string, index: number): string {
  const normalizedName = brandName.toLowerCase();
  const colorKeys = Object.keys(CHART_COLORS);
  
  // Use consistent color based on brand name if possible
  if (normalizedName.includes('blue')) return CHART_COLORS.blue;
  if (normalizedName.includes('green')) return CHART_COLORS.green;
  if (normalizedName.includes('red')) return CHART_COLORS.red;
  if (normalizedName.includes('purple')) return CHART_COLORS.purple;
  if (normalizedName.includes('orange')) return CHART_COLORS.orange;
  if (normalizedName.includes('yellow')) return CHART_COLORS.yellow;
  
  // Otherwise use index to assign a color
  return CHART_COLORS[colorKeys[index % colorKeys.length] as keyof typeof CHART_COLORS];
}

// Format a KPI value based on KPI name and currency
export function formatKpiValue(
  value: number | null, 
  kpiName: string, 
  currency: string = 'CAD'
): string {
  if (value === null || value === undefined) return 'N/A';
  
  // Handle percentage KPIs (those that start with %)
  if (kpiName.startsWith('%')) {
    return formatPercent(value);
  }
  
  // Handle currency-based KPIs
  if (
    kpiName.includes('Revenue') || 
    kpiName.includes('Margin') || 
    kpiName.includes('Cost') ||
    kpiName.includes('Spend') ||
    kpiName.includes('ROAS') ||
    kpiName.includes('Adspend') ||
    kpiName.includes('CPA') ||
    kpiName.includes('Discount')
  ) {
    return formatCurrency(value, currency);
  }
  
  // For all other numerical values
  return formatNumber(value);
}

// Format currency with compact notation
export function formatCompactCurrency(
  value: number | null | undefined,
  currency = "CAD",
  defaultValue = "N/A"
): string {
  if (value === null || value === undefined) return defaultValue;

  const symbol = currency === "USD" ? "$" : currency === "CAD" ? "$" : "£";
  return `${symbol}${value.toLocaleString(undefined, {
    notation: "compact",
    compactDisplay: "short",
    minimumFractionDigits: 0,
    maximumFractionDigits: 1,
  })}`;
}

// Format number with compact notation
export function formatCompactNumber(
  value: number | null | undefined,
  defaultValue = "N/A"
): string {
  if (value === null || value === undefined) return defaultValue;
  return value.toLocaleString(undefined, {
    notation: "compact",
    compactDisplay: "short",
  });
}

// For Recharts gradients
export function getGradientDefinition(id: string, color: string) {
  return {
    id: id,
    x1: 0,
    y1: 0,
    x2: 0,
    y2: 1,
    children: [
      <stop key="0" offset="5%" stopColor={color} stopOpacity={0.8} />,
      <stop key="1" offset="95%" stopColor={color} stopOpacity={0.1} />,
    ],
  };
}
