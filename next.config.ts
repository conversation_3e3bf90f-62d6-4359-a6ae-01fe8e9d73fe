import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    serverActions: {
      allowedOrigins: ["localhost:6699", "insights.nolk.com"],
    },
  },
  webpack: (config) => {
    // Exclude backup directories from the build
    config.watchOptions = {
      ...config.watchOptions,
      ignored: ['**/backup_*/**', '**/backups/**'],
    };
    return config;
  },
};

export default nextConfig;
